# نموذج معلومات هوية الراكب - Web Automator

## ✅ تم إضافة نموذج شامل لتسجيل معلومات هوية الراكب!

### 🎯 الميزة الجديدة:

#### **عند اختيار مسافر من القائمة المنسدلة:**
```
✅ يظهر نموذج معلومات الهوية تلقائياً:
┌─────────────────────────────────────────────────────────┐
│ معلومات هوية الراكب                                    │
│                                                         │
│ SR DOCS yy HK1- [p▼] / [SAU] / [__________]            │
│                                                         │
│ [SAU] / [01JAN80] / [m▼] /                             │
│                                                         │
│ [01SEP26] / AHMED/SARA /p1                             │
│                                                         │
│ [إنشاء أمر الهوية]                                     │
│                                                         │
│ ┌─────────────────────────────────────────────────────┐ │
│ │ الأمر المُنشأ:                                      │ │
│ │ SR DOCS yy HK1-p/SAU/1234567890/SAU/01JAN80/m/...  │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

### 🔧 مكونات النموذج:

#### **الصف الأول: البيانات الأساسية**
1. **SR DOCS yy HK1-** (ثابت)
2. **نوع الوثيقة:** قائمة منسدلة
   - `i` (هوية وطنية)
   - `p` (جواز سفر) - افتراضي
3. **الجنسية الأولى:** مربع نص
   - القيمة الافتراضية: `SAU`
   - قابل للتعديل
4. **رقم الهوية:** مربع نص
   - 10 أرقام بالضبط
   - مطلوب

#### **الصف الثاني: البيانات الشخصية**
1. **الجنسية الثانية:** مربع نص
   - القيمة الافتراضية: `SAU`
   - قابل للتعديل
2. **تاريخ الميلاد:** مربع نص
   - تنسيق: `01JAN80`
   - 7 أحرف بالضبط
   - مطلوب
3. **الجنس:** قائمة منسدلة
   - `m` (ذكر) - افتراضي
   - `f` (أنثى)

#### **الصف الثالث: البيانات النهائية**
1. **تاريخ انتهاء الهوية:** مربع نص
   - تنسيق: `01SEP26`
   - 7 أحرف بالضبط
   - مطلوب
2. **اسم المسافر:** يُملأ تلقائياً
   - من البيانات المحددة في القائمة
   - غير قابل للتعديل
3. **رقم المسافر:** يُملأ تلقائياً
   - تنسيق: `/p1`, `/p2`, إلخ
   - حسب رقم المسافر المحدد

### 🚀 كيفية الاستخدام:

#### **الخطوات:**
1. **اختيار مسافر:**
   - من القائمة المنسدلة
   - سيظهر نموذج الهوية تلقائياً

2. **ملء البيانات:**
   - **نوع الوثيقة:** اختر `i` أو `p`
   - **الجنسية:** عدّل إذا لزم الأمر (افتراضي SAU)
   - **رقم الهوية:** أدخل 10 أرقام
   - **تاريخ الميلاد:** بتنسيق `01JAN80`
   - **الجنس:** اختر `m` أو `f`
   - **تاريخ الانتهاء:** بتنسيق `01SEP26`

3. **إنشاء الأمر:**
   - انقر "إنشاء أمر الهوية"
   - سيتم التحقق من صحة البيانات
   - الأمر سيُنسخ تلقائياً للحافظة
   - سيظهر الأمر في منطقة العرض

### 🎯 تنسيق الأمر النهائي:

#### **الصيغة:**
```
SR DOCS yy HK1-{نوع_الوثيقة}/{الجنسية_1}/{رقم_الهوية}/{الجنسية_2}/{تاريخ_الميلاد}/{الجنس}/{تاريخ_الانتهاء}/{اسم_المسافر}/p{رقم_المسافر}
```

#### **مثال:**
```
SR DOCS yy HK1-p/SAU/1234567890/SAU/01JAN80/m/01SEP26/ALANAZI/MUDHHI MR/p1
```

### 🔍 التحقق من البيانات:

#### **التحققات التلقائية:**
- **رقم الهوية:** يجب أن يكون 10 أرقام بالضبط
- **تاريخ الميلاد:** يجب أن يكون 7 أحرف بالضبط
- **تاريخ الانتهاء:** يجب أن يكون 7 أحرف بالضبط
- **البيانات المطلوبة:** جميع الحقول مطلوبة

#### **رسائل الخطأ:**
- "يرجى إدخال رقم هوية صحيح (10 أرقام)"
- "يرجى إدخال تاريخ الميلاد بالتنسيق الصحيح (01JAN80)"
- "يرجى إدخال تاريخ انتهاء الهوية بالتنسيق الصحيح (01SEP26)"

### 🎨 التصميم والواجهة:

#### **نموذج الهوية:**
- خلفية بيضاء نظيفة
- حدود زرقاء مميزة
- تنظيم في 3 صفوف واضحة
- مسافات مناسبة بين العناصر

#### **الحقول:**
- حدود رمادية فاتحة
- تأثير أزرق عند التركيز
- خطوط واضحة وقابلة للقراءة
- أحجام مناسبة لكل نوع بيانات

#### **زر الإنشاء:**
- لون أخضر جذاب
- تأثير رفع عند التمرير
- نص واضح ومفهوم

#### **منطقة عرض الأمر:**
- خلفية رمادية فاتحة
- حدود خضراء
- خط أحادي المسافة للوضوح
- إمكانية نسخ سهلة

### 💡 الميزات المتقدمة:

#### **النسخ التلقائي:**
- الأمر يُنسخ تلقائياً للحافظة
- رسالة تأكيد تظهر لمدة 3 ثوان
- إمكانية لصق مباشرة في أي مكان

#### **العرض التفاعلي:**
- الأمر يظهر في منطقة منفصلة
- تنسيق واضح وقابل للقراءة
- سهولة مراجعة البيانات

#### **التحديث التلقائي:**
- اسم المسافر يُملأ تلقائياً
- رقم المسافر يُحدد تلقائياً
- القيم الافتراضية ذكية

### 🧪 أمثلة عملية:

#### **مسافر بالغ سعودي:**
```
الإدخال:
- نوع الوثيقة: p
- الجنسية: SAU
- رقم الهوية: 1133832657
- تاريخ الميلاد: 01JAN80
- الجنس: m
- تاريخ الانتهاء: 01SEP26
- المسافر: ALANAZI/MUDHHI MR (رقم 1)

النتيجة:
SR DOCS yy HK1-p/SAU/1133832657/SAU/01JAN80/m/01SEP26/ALANAZI/MUDHHI MR/p1
```

#### **مسافرة أجنبية:**
```
الإدخال:
- نوع الوثيقة: p
- الجنسية: USA
- رقم الهوية: 1234567890
- تاريخ الميلاد: 15MAR85
- الجنس: f
- تاريخ الانتهاء: 20DEC28
- المسافر: SMITH/JANE MRS (رقم 2)

النتيجة:
SR DOCS yy HK1-p/USA/1234567890/USA/15MAR85/f/20DEC28/SMITH/JANE MRS/p2
```

### 🔧 التفاصيل التقنية:

#### **JavaScript:**
```javascript
function generateIdentityCommand(passengerNumber) {
  // جمع البيانات من النموذج
  const docType = document.getElementById(`docType_${passengerNumber}`).value;
  const nationality = document.getElementById(`nationality_${passengerNumber}`).value;
  // ... باقي البيانات
  
  // إنشاء الأمر
  const command = `SR DOCS yy HK1-${docType}/${nationality}/${idNumber}/${nationality2}/${birthDate}/${gender}/${expiryDate}/${passengerName}/p${passengerNumber}`;
  
  // نسخ ونشر
  navigator.clipboard.writeText(command);
}
```

#### **HTML:**
```html
<div class="identity-section">
  <h5>معلومات هوية الراكب</h5>
  
  <div class="identity-row">
    <span>SR DOCS yy HK1-</span>
    <select id="docType_1">
      <option value="i">i</option>
      <option value="p" selected>p</option>
    </select>
    <input type="text" id="nationality_1" value="SAU">
    <input type="text" id="idNumber_1" placeholder="رقم الهوية">
  </div>
  
  <button class="generate-identity-btn">إنشاء أمر الهوية</button>
</div>
```

### 🎯 الفوائد:

#### **السرعة:**
- ملء تلقائي للبيانات الأساسية
- نسخ فوري للحافظة
- واجهة سريعة ومباشرة

#### **الدقة:**
- تحقق تلقائي من صحة البيانات
- تنسيق صحيح مضمون
- منع الأخطاء الشائعة

#### **سهولة الاستخدام:**
- واجهة بديهية وواضحة
- تنظيم منطقي للحقول
- رسائل مساعدة واضحة

### 🎯 الخلاصة:

#### **الميزات المضافة:**
- ✅ نموذج شامل لمعلومات الهوية
- ✅ تحقق تلقائي من صحة البيانات
- ✅ نسخ فوري للحافظة
- ✅ عرض تفاعلي للأمر المُنشأ
- ✅ واجهة أنيقة ومنظمة

#### **التحسينات:**
- ✅ **ملء تلقائي** للبيانات الأساسية
- ✅ **تحقق ذكي** من صحة الإدخال
- ✅ **نسخ سريع** للحافظة
- ✅ **عرض واضح** للأمر النهائي
- ✅ **تصميم احترافي** ومنظم

---

**تم إضافة نموذج معلومات هوية الراكب بنجاح! 🎯**

**الآن يمكن إنشاء أوامر الهوية بسرعة ودقة! ✅**

**أعد تحميل الإضافة وجرب الميزة الجديدة! 🚀**
