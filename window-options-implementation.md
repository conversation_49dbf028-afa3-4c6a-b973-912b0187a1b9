# تنفيذ خيار النافذة المنفصلة

## الحل المقترح: خيار مختلط

### 1. الاحتفاظ بالنافذة المنبثقة الحالية
- تبقى كما هي للاستخدام السريع
- سريعة ومباشرة

### 2. إضافة زر "فتح في نافذة منفصلة"
```html
<div class="window-controls" style="text-align: center; margin-top: 10px;">
  <button id="openInWindow" class="btn btn-secondary btn-sm">
    🪟 فتح في نافذة منفصلة
  </button>
</div>
```

### 3. الكود المطلوب:

#### في popup.js:
```javascript
// معالج فتح النافذة المنفصلة
document.getElementById('openInWindow').addEventListener('click', () => {
  chrome.windows.create({
    url: chrome.runtime.getURL('popup.html') + '?standalone=true',
    type: 'popup',
    width: 500,
    height: 700,
    focused: true
  });
  
  // إغلاق النافذة المنبثقة الحالية
  window.close();
});

// التحقق من وضع النافذة المنفصلة
function isStandaloneMode() {
  return new URLSearchParams(window.location.search).get('standalone') === 'true';
}

// تعديل الواجهة للنافذة المنفصلة
if (isStandaloneMode()) {
  document.body.style.width = '480px';
  document.body.style.minHeight = '680px';
  
  // إخفاء زر "فتح في نافذة منفصلة"
  const openInWindowBtn = document.getElementById('openInWindow');
  if (openInWindowBtn) {
    openInWindowBtn.style.display = 'none';
  }
}
```

#### في popup.html:
```html
<!-- إضافة الزر في نهاية الواجهة -->
<div class="window-controls" style="text-align: center; margin-top: 15px; padding-top: 10px; border-top: 1px solid #e9ecef;">
  <button id="openInWindow" class="btn btn-secondary btn-sm" style="font-size: 11px; padding: 6px 12px;">
    🪟 فتح في نافذة منفصلة
  </button>
</div>
```

## المزايا:

### للنافذة المنبثقة:
- ✅ سريعة ومباشرة
- ✅ لا تشغل مساحة إضافية
- ✅ مناسبة للاستخدام السريع

### للنافذة المنفصلة:
- ✅ مساحة أكبر للعمل
- ✅ لا تُغلق تلقائياً
- ✅ يمكن تحريكها وتغيير حجمها
- ✅ أفضل للعمل المطول

## التنفيذ التدريجي:

### المرحلة 1: إضافة الزر
- إضافة زر "فتح في نافذة منفصلة"
- اختبار الوظيفة الأساسية

### المرحلة 2: تحسين النافذة المنفصلة
- تعديل الأبعاد والتخطيط
- إضافة ميزات خاصة بالنافذة المنفصلة

### المرحلة 3: حفظ تفضيلات المستخدم
- حفظ اختيار المستخدم (نافذة منبثقة أم منفصلة)
- فتح النوع المفضل تلقائياً

## الكود الكامل للتنفيذ:

### 1. تعديل popup.html:
```html
<!-- في نهاية الـ container قبل إغلاق </div> -->
<div class="window-controls" style="text-align: center; margin-top: 15px; padding-top: 10px; border-top: 1px solid #e9ecef;">
  <button id="openInWindow" class="btn btn-secondary btn-sm" style="font-size: 11px; padding: 6px 12px;">
    🪟 فتح في نافذة منفصلة
  </button>
</div>
```

### 2. تعديل popup.js:
```javascript
// في نهاية دالة DOMContentLoaded
document.getElementById('openInWindow')?.addEventListener('click', () => {
  chrome.windows.create({
    url: chrome.runtime.getURL('popup.html') + '?standalone=true',
    type: 'popup',
    width: 500,
    height: 700,
    focused: true
  });
  window.close();
});

// تعديل الواجهة للنافذة المنفصلة
if (new URLSearchParams(window.location.search).get('standalone') === 'true') {
  document.body.style.width = '480px';
  document.body.style.minHeight = '680px';
  document.getElementById('openInWindow')?.remove();
}
```

## النتيجة:
- المستخدم يحصل على خيار الاستخدام
- النافذة المنبثقة تبقى للاستخدام السريع
- النافذة المنفصلة متاحة للعمل المطول
- لا توجد مشاكل تقنية
