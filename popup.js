// نظام السجلات للـ popup
const logger = {
  log: (message, data = null) => {
    console.log(`[Popup] ${message}`, data);
  },
  error: (message, error = null) => {
    console.error(`[Popup Error] ${message}`, error);
    showStatus(`خطأ: ${message}`, 'error');
  }
};

// دالة لإظهار الحالة للمستخدم
function showStatus(message, type = 'info') {
  const statusDiv = document.getElementById('status') || createStatusDiv();
  statusDiv.textContent = message;
  statusDiv.className = `status ${type}`;
  statusDiv.style.display = 'block';

  // إخفاء الرسالة بعد 3 ثوان
  setTimeout(() => {
    statusDiv.style.display = 'none';
  }, 3000);
}

function createStatusDiv() {
  const statusDiv = document.createElement('div');
  statusDiv.id = 'status';
  statusDiv.style.cssText = `
    position: fixed;
    top: 10px;
    left: 10px;
    right: 10px;
    z-index: 1000;
    padding: 10px;
    border-radius: 6px;
    font-size: 12px;
    text-align: center;
    display: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  `;
  document.body.appendChild(statusDiv);
  return statusDiv;
}

// إدارة التبويبات - معطلة في الواجهة المبسطة
function initTabs() {
  // التبويبات مخفية في الواجهة المبسطة - لا حاجة لتفعيلها
  console.log('التبويبات معطلة في الواجهة المبسطة');
}

// إدارة منشئ الأوامر
function initCommandBuilder() {
  const commandType = document.getElementById('commandType');
  const valueGroup = document.getElementById('valueGroup');
  const attributeGroup = document.getElementById('attributeGroup');

  commandType.addEventListener('change', () => {
    const type = commandType.value;

    // إخفاء جميع المجموعات الاختيارية
    valueGroup.style.display = 'none';
    attributeGroup.style.display = 'none';

    // إظهار المجموعات المطلوبة حسب نوع الأمر
    switch (type) {
      case 'input':
      case 'inputWithEnter':
        valueGroup.style.display = 'block';
        break;
      case 'getAttribute':
        attributeGroup.style.display = 'block';
        break;
    }
  });
}

// دالة مساعدة للحصول على التبويب النشط
async function getCurrentTab() {
  try {
    // التحقق من وضع النافذة المنفصلة
    const isStandalone = new URLSearchParams(window.location.search).get('standalone') === 'true';

    if (isStandalone) {
      // في النافذة المنفصلة، نحاول استخدام معرف التبويب المحفوظ أولاً
      const targetTabId = new URLSearchParams(window.location.search).get('targetTab');
      if (targetTabId) {
        try {
          const tab = await chrome.tabs.get(parseInt(targetTabId));
          if (tab && !tab.url.startsWith('chrome-extension://')) {
            return tab;
          }
        } catch (error) {
          console.log('التبويب المحفوظ لم يعد متاحاً، البحث عن بديل...');
        }
      }

      // البحث عن التبويب النشط في جميع النوافذ
      const tabs = await chrome.tabs.query({active: true});
      // نبحث عن التبويب الذي ليس extension page
      const browserTab = tabs.find(tab => !tab.url.startsWith('chrome-extension://'));
      if (browserTab) {
        return browserTab;
      }
      // إذا لم نجد، نأخذ أول تبويب نشط
      if (tabs.length > 0) {
        return tabs[0];
      }
    } else {
      // في النافذة المنبثقة العادية، نستخدم الطريقة الأصلية
      const tabs = await chrome.tabs.query({active: true, currentWindow: true});
      if (tabs && tabs.length > 0) {
        return tabs[0];
      }
    }

    throw new Error('لا يوجد تبويب نشط');
  } catch (error) {
    logger.error('فشل في الحصول على التبويب النشط', error);
    throw error;
  }
}

// دالة مساعدة لإرسال رسالة للتبويب
async function sendMessageToTab(tabId, message) {
  return new Promise((resolve, reject) => {
    chrome.tabs.sendMessage(tabId, message, (response) => {
      if (chrome.runtime.lastError) {
        reject(new Error(chrome.runtime.lastError.message));
        return;
      }

      if (!response) {
        reject(new Error('لم يتم الحصول على رد من الصفحة'));
        return;
      }

      if (!response.success) {
        reject(new Error(response.error || 'عملية غير ناجحة'));
        return;
      }

      resolve(response);
    });
  });
}

// معالج جلب الأسماء
document.getElementById('fetchNames').addEventListener('click', async () => {
  const button = document.getElementById('fetchNames');
  const resultArea = document.getElementById('result');

  try {
    button.disabled = true;
    button.textContent = 'جاري الجلب...';
    showStatus('جاري جلب الأسماء...', 'info');

    const tab = await getCurrentTab();
    const response = await sendMessageToTab(tab.id, {action: 'fetchNames'});

    if (response.names && response.names.length > 0) {
      resultArea.value = response.names.join('\n');
      showStatus(`تم جلب ${response.count} اسم بنجاح`, 'success');
      logger.log(`تم جلب ${response.count} اسم`, response);
    } else {
      resultArea.value = 'لم يتم العثور على أسماء';
      showStatus('لم يتم العثور على أسماء', 'warning');
    }

  } catch (error) {
    logger.error('فشل في جلب الأسماء', error);
    resultArea.value = `خطأ: ${error.message}`;
  } finally {
    button.disabled = false;
    button.textContent = 'جلب الأسماء من الصفحة';
  }
});

// معالج إرسال الأمر المتقدم
document.getElementById('sendCommand').addEventListener('click', async () => {
  const button = document.getElementById('sendCommand');
  const resultArea = document.getElementById('result');

  try {
    button.disabled = true;

    button.textContent = 'جاري التنفيذ...';
    showStatus('جاري تنفيذ الأمر...', 'info');

    const tab = await getCurrentTab();

    // جمع بيانات الأمر من الواجهة
    const commandData = {
      action: 'sendCommand',
      command: document.getElementById('commandType').value,
      selector: document.getElementById('elementSelector').value.trim(),
      waitTime: parseInt(document.getElementById('waitTime').value) || 0
    };

    // إضافة البيانات الإضافية حسب نوع الأمر
    switch (commandData.command) {
      case 'input':
      case 'inputWithEnter':
        commandData.value = document.getElementById('inputValue').value;
        break;
      case 'getAttribute':
        commandData.attribute = document.getElementById('attributeName').value.trim();
        break;
    }

    // التحقق من صحة البيانات
    if (!commandData.selector) {
      throw new Error('يجب تحديد محدد العنصر');
    }

    if (commandData.command === 'input' && !commandData.value && commandData.value !== '') {
      throw new Error('يجب إدخال القيمة للكتابة');
    }

    if (commandData.command === 'getAttribute' && !commandData.attribute) {
      throw new Error('يجب تحديد اسم الخاصية');
    }

    const response = await sendMessageToTab(tab.id, commandData);

    // عرض النتيجة
    let resultText = `✅ تم تنفيذ الأمر بنجاح\n`;
    resultText += `الأمر: ${commandData.command}\n`;
    resultText += `المحدد: ${commandData.selector}\n`;
    resultText += `الوقت: ${new Date().toLocaleTimeString('ar-SA')}\n`;

    if (response.result) {
      if (response.result.text) {
        resultText += `النص: ${response.result.text}\n`;
      }
      if (response.result.value !== undefined) {
        resultText += `القيمة: ${response.result.value}\n`;
      }
    }

    resultText += '\n' + (resultArea.value || '');
    resultArea.value = resultText;

    showStatus('تم تنفيذ الأمر بنجاح', 'success');
    logger.log('تم تنفيذ الأمر بنجاح', response);

  } catch (error) {
    logger.error('فشل في تنفيذ الأمر', error);
    const errorText = `❌ فشل في تنفيذ الأمر: ${error.message}\n${new Date().toLocaleTimeString('ar-SA')}\n\n${document.getElementById('result').value || ''}`;
    document.getElementById('result').value = errorText;
  } finally {
    button.disabled = false;
    button.textContent = 'تنفيذ الأمر';
  }
});

// معالجات الإجراءات السريعة - معطلة في الواجهة المبسطة
// هذه المعالجات محفوظة للوظائف الداخلية ولكنها مخفية من الواجهة
function initQuickActions() {
  // الأزرار مخفية - لا حاجة لتفعيل المعالجات
  console.log('الإجراءات السريعة معطلة في الواجهة المبسطة');
}

// معالجات الأوامر المخصصة - محفوظة للوظائف الداخلية
async function executeCustomInput(value) {
  if (value === null) return;

  // استخدام المحدد المخصص من الإعدادات
  const result = await chrome.storage.sync.get('customInputSelector');
  const selector = result.customInputSelector || '#tpl0_shellbridge_shellWindow_top_left_modeString_cmdPromptInput';

  await executeQuickCommand('inputWithEnter', selector, value);
}

async function executeCustomOutput() {
  // استخدام المحدد المخصص من الإعدادات
  const result = await chrome.storage.sync.get('customOutputSelector');
  const selector = result.customOutputSelector || '[id*="cmdResponse"][id*="_speedMode"] > pre, [id*="cmdResponse"][id*="_speedModeTarget"]';

  await executeQuickCommand('getText', selector);
}

// معالج جلب الأسماء في مربعات نص
document.getElementById('extractNames').addEventListener('click', async () => {
  try {
    showStatus('جاري جلب الأسماء...', 'info');

    // الحصول على المحدد المخصص للمخرجات
    const result = await chrome.storage.sync.get('customOutputSelector');
    const selector = result.customOutputSelector || '[id*="cmdResponse"][id*="_speedMode"] > pre, [id*="cmdResponse"][id*="_speedModeTarget"]';

    // جلب النص من المخرجات
    const tab = await getCurrentTab();
    const response = await sendMessageToTab(tab.id, {
      action: 'sendCommand',
      command: 'getText',
      selector: selector
    });

    if (response.success && response.result?.text) {
      console.log('النص المستخرج من المخرجات:');
      console.log(response.result.text);
      console.log('---');

      // استخراج الأسماء من النص
      const passengers = extractNamesFromText(response.result.text);

      console.log('المسافرون المستخرجون:', passengers);
      console.log('عدد المسافرين:', passengers.length);

      if (passengers.length > 0) {
        displayNamesInTextBoxes(passengers);
        showStatus(`تم استخراج ${passengers.length} مسافر`, 'success');
      } else {
        showStatus('لم يتم العثور على أسماء في النص', 'warning');
      }
    } else {
      throw new Error('فشل في جلب النص من المخرجات');
    }

  } catch (error) {
    logger.error('فشل في جلب الأسماء', error);
    showStatus(`خطأ: ${error.message}`, 'error');
  }
});

// معالج زر تحديث الأسماء
document.getElementById('refreshNames').addEventListener('click', async () => {
  const button = document.getElementById('refreshNames');

  try {
    button.disabled = true;
    button.textContent = 'جاري التحديث...';

    // استدعاء دالة الجلب التلقائي مباشرة (تتجاهل الإعداد)
    await forceAutoFetchNames();

  } catch (error) {
    logger.error('فشل في تحديث الأسماء', error);
    showStatus(`خطأ: ${error.message}`, 'error');
  } finally {
    button.disabled = false;
    button.textContent = 'تحديث الأسماء';
  }
});

// دالة الجلب التلقائي القسري (تتجاهل الإعداد)
async function forceAutoFetchNames() {
  try {
    console.log('بدء تحديث الأسماء...');
    showStatus('جاري تحديث الأسماء...', 'info');

    // الحصول على المحدد المخصص للمخرجات
    const result = await chrome.storage.sync.get('customOutputSelector');
    const selector = result.customOutputSelector || '[id*="cmdResponse"][id*="_speedMode"] > pre, [id*="cmdResponse"][id*="_speedModeTarget"]';

    // جلب النص من المخرجات
    const tab = await getCurrentTab();
    const response = await sendMessageToTab(tab.id, {
      action: 'sendCommand',
      command: 'getText',
      selector: selector
    });

    if (response.success && response.result?.text) {
      console.log('تم تحديث النص من المخرجات');

      // استخراج الأسماء من النص
      const passengers = extractNamesFromText(response.result.text);

      if (passengers.length > 0) {
        displayNamesInTextBoxes(passengers);
        showStatus(`تم تحديث ${passengers.length} مسافر`, 'success');
        console.log(`تم تحديث ${passengers.length} مسافر`);
      } else {
        showStatus('لم يتم العثور على أسماء في الصفحة', 'warning');
        console.log('لم يتم العثور على أسماء في النص المحدث');
      }
    } else {
      throw new Error('فشل في جلب النص من المخرجات');
    }

  } catch (error) {
    console.error('فشل في تحديث الأسماء:', error);
    throw error;
  }
}

// معالج زر تسجيل رقم الجوال
document.getElementById('registerPhone').addEventListener('click', async () => {
  try {
    const phoneInput = document.getElementById('phoneInput');
    const phoneNumber = phoneInput.value.trim();

    // التحقق من صحة رقم الجوال
    if (!phoneNumber) {
      showStatus('يرجى إدخال رقم الجوال', 'warning');
      phoneInput.focus();
      return;
    }

    if (!/^[0-9]{9}$/.test(phoneNumber)) {
      showStatus('يرجى إدخال رقم جوال صحيح (9 أرقام)', 'warning');
      phoneInput.focus();
      return;
    }

    if (!phoneNumber.startsWith('5')) {
      showStatus('رقم الجوال يجب أن يبدأ بالرقم 5', 'warning');
      phoneInput.focus();
      return;
    }

    showStatus('جاري تسجيل رقم الجوال...', 'info');

    // الحصول على عدد المسافرين من الأسماء المستخرجة
    let travelerCount = getTravelerCount();
    const hasExtractedNames = checkIfNamesExist();

    // إذا لم نجد أسماء مستخرجة، اطلب من المستخدم إدخال العدد
    if (!hasExtractedNames) {
      const userCount = prompt('لم يتم استخراج أسماء بعد. كم عدد المسافرين في هذا الحجز؟', '1');
      if (userCount && !isNaN(userCount) && parseInt(userCount) > 0) {
        travelerCount = parseInt(userCount);
      }
    }

    console.log('العدد النهائي للمسافرين:', travelerCount);
    console.log('هل توجد أسماء مستخرجة:', hasExtractedNames);

    // تسجيل رقم الجوال بالطرق الثلاث
    await registerPhoneNumber(phoneNumber, travelerCount);

    const statusMessage = hasExtractedNames
      ? `تم تسجيل رقم الجوال بنجاح لـ ${travelerCount} مسافر (تم الحساب تلقائياً)`
      : `تم تسجيل رقم الجوال بنجاح لـ ${travelerCount} مسافر (تم الإدخال يدوياً)`;

    showStatus(statusMessage, 'success');

  } catch (error) {
    logger.error('فشل في تسجيل رقم الجوال', error);
    showStatus(`خطأ: ${error.message}`, 'error');
  }
});

// دالة مساعدة لتنفيذ الأوامر السريعة
async function executeQuickCommand(command, selector, value = null) {
  const resultArea = document.getElementById('result');

  try {
    showStatus(`جاري تنفيذ ${command}...`, 'info');

    const tab = await getCurrentTab();
    const commandData = {
      action: command === 'waitForElement' ? 'waitForElement' : 'sendCommand',
      command: command,
      selector: selector
    };

    if (value !== null) {
      commandData.value = value;
    }

    const response = await sendMessageToTab(tab.id, commandData);

    let resultText = `✅ ${command} - نجح\n`;
    resultText += `المحدد: ${selector}\n`;
    if (value) resultText += `القيمة: ${value}\n`;
    if (response.result?.text) resultText += `النص: ${response.result.text}\n`;
    resultText += `الوقت: ${new Date().toLocaleTimeString('ar-SA')}\n\n`;

    resultArea.value = resultText + (resultArea.value || '');
    showStatus('تم التنفيذ بنجاح', 'success');

  } catch (error) {
    logger.error(`فشل في تنفيذ ${command}`, error);
    const errorText = `❌ ${command} - فشل: ${error.message}\n${new Date().toLocaleTimeString('ar-SA')}\n\n`;
    resultArea.value = errorText + (resultArea.value || '');
  }
}

// دالة استخراج الأسماء من النص (الطريقة الصحيحة الجديدة)
function extractNamesFromText(text) {
  if (!text || typeof text !== 'string') {
    return [];
  }

  const passengers = []; // مصفوفة تحتوي على كائنات {name, type}
  console.log('بدء استخراج الأسماء من النص بالطريقة الصحيحة...');
  console.log('النص الكامل:', text);

  // البحث التسلسلي للمسافرين: 1.اسم ثم 2.اسم وهكذا حتى نصل لرقم الرحلة
  let currentNumber = 1;
  let foundPassengers = true;

  while (foundPassengers && currentNumber <= 50) { // حد أقصى 50 مسافر
    console.log(`البحث عن المسافر رقم ${currentNumber}...`);

    // نمط البحث محسن للأسماء المركبة: رقم.اسم مسافات/اسم (مع أو بدون لقب) (مع أو بدون أقواس)
    const passengerPattern = new RegExp(
      `${currentNumber}\\.((?:[A-Z]+(?:\\s+[A-Z]+)*)\\/(?:[A-Z]+(?:\\s+[A-Z]+)*)(?:\\s+(?:MR|MRS|MS|MISS|DR|PROF|MSTR))*?)(?:\\s*\\(([^)]+)\\))?`,
      'gi'
    );

    const match = passengerPattern.exec(text);

    if (match) {
      let fullMatch = match[0];
      let nameWithTitle = match[1].trim();
      let typeInParentheses = match[2] ? match[2].trim().toUpperCase() : null;

      console.log(`وجد المسافر رقم ${currentNumber}:`, fullMatch);
      console.log('الاسم مع اللقب:', nameWithTitle);
      console.log('النوع في الأقواس:', typeInParentheses);

      // تنظيف الاسم (إزالة الألقاب والأقواس مع الحفاظ على المسافات في الأسماء المركبة)
      let cleanName = nameWithTitle;

      // إزالة الأقواس وما بداخلها من الاسم
      cleanName = cleanName.replace(/\s*\([^)]*\)\s*/g, '').trim();

      // إزالة الألقاب من النهاية (مع إضافة MSTR للأطفال)
      cleanName = cleanName.replace(/\s+(MR|MRS|MS|MISS|DR|PROF|CHD|ADT|MSTR)$/i, '').trim();

      // تنظيف المسافات الزائدة مع الحفاظ على المسافات في الأسماء المركبة
      cleanName = cleanName.replace(/\s+/g, ' ').trim();

      // تحديد نوع الراكب ومعالجة الرضع (INF)
      let passengerType = 'ADT'; // افتراضي
      let finalName = cleanName; // الاسم النهائي

      if (typeInParentheses) {
        console.log('معالجة محتوى الأقواس:', typeInParentheses);

        // التحقق من وجود رضيع (INF)
        if (typeInParentheses.includes('INF')) {
          // أولاً: إضافة المسافر البالغ (الذي معه الرضيع)
          console.log(`إضافة المسافر البالغ ${currentNumber}:`, cleanName);
          if (cleanName.includes('/') && cleanName.length > 3) {
            passengers.push({
              name: cleanName,
              type: 'ADT',
              seatNumber: currentNumber  // رقم المقعد للبالغ
            });
            console.log(`تم إضافة المسافر البالغ ${currentNumber}:`, { name: cleanName, type: 'ADT', seatNumber: currentNumber });
          }

          // ثانياً: إضافة الرضيع بنفس رقم المقعد
          let infantName = '';

          // الطريقة الأولى: INF/اسم_الرضيع (نفس اسم العائلة)
          const infPattern1 = /INF\/([A-Z]+(?:\s+[A-Z]+)*)/i;
          const infMatch1 = infPattern1.exec(typeInParentheses);

          if (infMatch1) {
            // استخراج اسم العائلة من الاسم الأصلي
            const familyName = cleanName.split('/')[0];
            const infantFirstName = infMatch1[1].trim();
            infantName = `${familyName}/${infantFirstName}`;
            console.log(`رضيع - الطريقة الأولى: ${infantName}`);
          } else {
            // الطريقة الثانية: INFاسم_العائلة/اسم_الرضيع
            const infPattern2 = /INF([A-Z]+(?:\s+[A-Z]+)*)\/([A-Z]+(?:\s+[A-Z]+)*)/i;
            const infMatch2 = infPattern2.exec(typeInParentheses);

            if (infMatch2) {
              const infantFamilyName = infMatch2[1].trim();
              const infantFirstName = infMatch2[2].trim();
              infantName = `${infantFamilyName}/${infantFirstName}`;
              console.log(`رضيع - الطريقة الثانية: ${infantName}`);
            }
          }

          // إضافة الرضيع إذا تم استخراج اسمه بنجاح (بنفس رقم المقعد)
          if (infantName.includes('/') && infantName.length > 3) {
            passengers.push({
              name: infantName,
              type: 'INF',
              seatNumber: currentNumber  // نفس رقم المقعد للبالغ
            });
            console.log(`تم إضافة الرضيع برقم المقعد ${currentNumber}:`, { name: infantName, type: 'INF', seatNumber: currentNumber });
          }

          // لا نحتاج لمعالجة إضافية لأننا أضفنا كلا المسافرين
          currentNumber++;
          continue;

        } else if (typeInParentheses.includes('CHD')) {
          passengerType = 'CHD';
        } else if (typeInParentheses.includes('ADT')) {
          passengerType = 'ADT';
        } else {
          passengerType = typeInParentheses;
        }
      } else {
        // إذا لم يكن هناك أقواس، تحقق من وجود CHD في الاسم الأصلي
        if (nameWithTitle.toUpperCase().includes('CHD')) {
          passengerType = 'CHD';
        }
      }

      console.log(`الاسم النظيف للمسافر ${currentNumber}:`, cleanName);
      console.log(`الاسم النهائي للمسافر ${currentNumber}:`, finalName);
      console.log(`نوع المسافر ${currentNumber}:`, passengerType);

      // التأكد من صحة الاسم (للمسافرين العاديين)
      if (finalName.includes('/') && finalName.length > 3) {
        passengers.push({
          name: finalName,
          type: passengerType,
          seatNumber: currentNumber  // رقم المقعد
        });
        console.log(`تم إضافة المسافر ${currentNumber}:`, { name: finalName, type: passengerType, seatNumber: currentNumber });
      }

      currentNumber++;
      // إعادة تعيين النمط للبحث من البداية
      passengerPattern.lastIndex = 0;
    } else {
      // تحقق من وجود رقم رحلة (مثل 3 SV1482)
      const flightPattern = new RegExp(`${currentNumber}\\s+[A-Z]{2}\\d+`, 'gi');
      const flightMatch = flightPattern.exec(text);

      if (flightMatch) {
        console.log(`وجد رقم رحلة في الموضع ${currentNumber}:`, flightMatch[0]);
        console.log('توقف البحث عن المسافرين');
        foundPassengers = false;
      } else {
        console.log(`لم يتم العثور على المسافر رقم ${currentNumber} أو رقم رحلة`);
        foundPassengers = false;
      }
    }
  }

  console.log('إجمالي المسافرين الموجودين:', passengers.length);
  console.log('قائمة المسافرين:', passengers);

  return passengers;
}



// دالة للحصول على عدد المسافرين
function getTravelerCount() {
  const dropdown = document.getElementById('namesDropdown');

  if (dropdown && dropdown.options.length > 1) {
    // حساب عدد المسافرين من القائمة المنسدلة
    let seatCount = 0;
    let totalCount = dropdown.options.length - 1; // استثناء الخيار الافتراضي

    // فحص كل خيار في القائمة
    for (let i = 1; i < dropdown.options.length; i++) {
      const option = dropdown.options[i];
      const passengerType = option.getAttribute('data-type');

      if (passengerType !== 'INF') {
        seatCount++;
      }
      console.log(`المسافر ${i}: ${passengerType} - ${passengerType === 'INF' ? 'لا يحتسب' : 'يحتسب'}`);
    }

    console.log(`إجمالي المسافرين: ${totalCount}, المسافرين بمقاعد: ${seatCount}`);
    return Math.max(1, seatCount);
  }

  // إذا لم توجد القائمة المنسدلة، جرب الطريقة القديمة
  const namesContainer = document.getElementById('namesContainer');
  const nameInputs = namesContainer.querySelectorAll('.name-input');
  const totalCount = nameInputs.length;

  console.log('إجمالي مربعات الأسماء الموجودة:', totalCount);

  if (totalCount === 0) {
    // جرب البحث في النص المعروض
    const extractedNames = document.querySelectorAll('.name-item');
    const extractedCount = extractedNames.length;
    console.log('عدد الأسماء المستخرجة:', extractedCount);
    return Math.max(1, extractedCount);
  }

  return Math.max(1, totalCount);
}

// دالة للتحقق من وجود أسماء مستخرجة
function checkIfNamesExist() {
  // التحقق من القائمة المنسدلة
  const dropdown = document.getElementById('namesDropdown');
  if (dropdown && dropdown.options.length > 1) {
    console.log('توجد أسماء في القائمة المنسدلة:', dropdown.options.length - 1);
    return true;
  }

  // التحقق من مربعات الأسماء القديمة
  const namesContainer = document.getElementById('namesContainer');
  if (namesContainer) {
    const nameInputs = namesContainer.querySelectorAll('.name-input');
    if (nameInputs.length > 0) {
      console.log('توجد مربعات أسماء:', nameInputs.length);
      return true;
    }
  }

  // التحقق من الأسماء المستخرجة
  const extractedNames = document.querySelectorAll('.name-item');
  if (extractedNames.length > 0) {
    console.log('توجد أسماء مستخرجة:', extractedNames.length);
    return true;
  }

  console.log('لا توجد أسماء مستخرجة');
  return false;
}

// دالة تسجيل رقم الجوال بالطريقة المبسطة
async function registerPhoneNumber(phoneNumber, travelerCount) {
  const fullPhoneNumber = `+966${phoneNumber}`;
  const tab = await getCurrentTab();

  console.log('تسجيل رقم الجوال:', fullPhoneNumber);
  console.log('عدد المسافرين:', travelerCount);

  // الحصول على المحدد المخصص للإدخال
  const result = await chrome.storage.sync.get('customInputSelector');
  const inputSelector = result.customInputSelector || '#tpl0_shellbridge_shellWindow_top_left_modeString_cmdPromptInput';

  // الطريقة الأولى: APM-+(رقم الجوال)
  const apmCommand = `APM-${fullPhoneNumber}`;
  console.log('إرسال الأمر الأول:', apmCommand);
  await sendCommandWithEnter(tab.id, inputSelector, apmCommand);

  // انتظار قصير بين الأوامر
  await new Promise(resolve => setTimeout(resolve, 1000));

  // الطريقة الثانية: APN-SV/M+(رقم الجوال)/AR/P1 أو P1-عدد المسافرين
  let apnCommand;
  if (travelerCount === 1) {
    apnCommand = `APN-SV/M${fullPhoneNumber}/AR/P1`;
    console.log('إرسال الأمر الثاني (مسافر واحد):', apnCommand);
  } else {
    apnCommand = `APN-SV/M${fullPhoneNumber}/AR/P1-${travelerCount}`;
    console.log(`إرسال الأمر الثاني (${travelerCount} مسافرين):`, apnCommand);
  }
  await sendCommandWithEnter(tab.id, inputSelector, apnCommand);

  // انتظار قصير بين الأوامر
  await new Promise(resolve => setTimeout(resolve, 1000));

  // الطريقة الثالثة: APM-SV/M+966(رقم الجوال)/AR/P1 أو P1-عدد المسافرين
  let apmSvCommand;
  if (travelerCount === 1) {
    apmSvCommand = `APM-SV/M${fullPhoneNumber}/AR/P1`;
    console.log('إرسال الأمر الثالث (مسافر واحد):', apmSvCommand);
  } else {
    apmSvCommand = `APM-SV/M${fullPhoneNumber}/AR/P1-${travelerCount}`;
    console.log(`إرسال الأمر الثالث (${travelerCount} مسافرين):`, apmSvCommand);
  }
  await sendCommandWithEnter(tab.id, inputSelector, apmSvCommand);
}

// دالة مساعدة لإرسال أمر مع الضغط على Enter
async function sendCommandWithEnter(tabId, selector, command) {
  // كتابة الأمر
  await sendMessageToTab(tabId, {
    action: 'sendCommand',
    command: 'inputWithEnter',
    selector: selector,
    value: command
  });
}

// دالة عرض الأسماء في قائمة منسدلة
function displayNamesInTextBoxes(passengers) {
  const namesSection = document.getElementById('namesSection');
  const namesContainer = document.getElementById('namesContainer');
  const namesCount = document.getElementById('namesCount');

  // مسح المحتوى السابق
  namesContainer.innerHTML = '';

  // حساب عدد المقاعد (استثناء الرضع)
  const seatCount = passengers.filter(p => p.type !== 'INF').length;
  const infantCount = passengers.filter(p => p.type === 'INF').length;

  // تحديث العداد
  let countText = `${passengers.length} اسم`;
  if (infantCount > 0) {
    countText += ` (${seatCount} مقعد + ${infantCount} رضيع)`;
  }
  namesCount.textContent = countText;

  // إنشاء القائمة المنسدلة
  const dropdown = document.createElement('select');
  dropdown.id = 'namesDropdown';
  dropdown.className = 'names-dropdown';
  dropdown.style.width = '100%';
  dropdown.style.padding = '10px';
  dropdown.style.fontSize = '14px';
  dropdown.style.border = '2px solid #ddd';
  dropdown.style.borderRadius = '5px';
  dropdown.style.backgroundColor = '#fff';
  dropdown.style.marginBottom = '10px';

  // إضافة خيار افتراضي
  const defaultOption = document.createElement('option');
  defaultOption.value = '';
  defaultOption.textContent = `اختر مسافر (${passengers.length} مسافر)`;
  defaultOption.disabled = true;
  defaultOption.selected = true;
  dropdown.appendChild(defaultOption);

  // إضافة خيار لكل مسافر
  passengers.forEach((passenger, index) => {
    const option = document.createElement('option');
    const displayNumber = passenger.seatNumber || (index + 1);

    // تحديد النص حسب نوع المسافر
    let passengerText = `${displayNumber}. ${passenger.name} - ${passenger.type}`;
    if (passenger.type === 'INF') {
      passengerText += ` (رضيع مع المسافر ${displayNumber})`;
    }

    option.value = index;
    option.textContent = passengerText;
    option.setAttribute('data-name', passenger.name);
    option.setAttribute('data-type', passenger.type);
    option.setAttribute('data-seat', displayNumber);

    dropdown.appendChild(option);
  });

  // إضافة القائمة المنسدلة للحاوية
  namesContainer.appendChild(dropdown);

  // إنشاء منطقة عرض تفاصيل المسافر المحدد
  const detailsContainer = document.createElement('div');
  detailsContainer.id = 'passengerDetails';
  detailsContainer.className = 'passenger-details';
  detailsContainer.style.display = 'none';
  detailsContainer.style.padding = '15px';
  detailsContainer.style.border = '2px solid #ddd';
  detailsContainer.style.borderRadius = '5px';
  detailsContainer.style.backgroundColor = '#f9f9f9';
  detailsContainer.style.marginTop = '10px';

  namesContainer.appendChild(detailsContainer);

  // إضافة مستمع للقائمة المنسدلة
  dropdown.addEventListener('change', function() {
    const selectedIndex = this.value;
    if (selectedIndex !== '') {
      const passenger = passengers[selectedIndex];
      const displayNumber = passenger.seatNumber || (parseInt(selectedIndex) + 1);
      showPassengerDetails(passenger, displayNumber, detailsContainer);
    } else {
      detailsContainer.style.display = 'none';
    }
  });

  // إظهار القسم
  namesSection.style.display = 'block';

  // التمرير إلى القسم
  namesSection.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

// دالة عرض تفاصيل المسافر المحدد
function showPassengerDetails(passenger, displayNumber, container) {
  // مسح المحتوى السابق
  container.innerHTML = '';

  // إنشاء عنوان
  const title = document.createElement('h4');
  title.textContent = `تفاصيل المسافر رقم ${displayNumber}`;
  title.style.margin = '0 0 15px 0';
  title.style.color = '#333';
  container.appendChild(title);

  // إنشاء صف التفاصيل
  const detailsRow = document.createElement('div');
  detailsRow.style.display = 'flex';
  detailsRow.style.alignItems = 'center';
  detailsRow.style.gap = '10px';
  detailsRow.style.flexWrap = 'wrap';

  // رقم المسافر
  const numberSpan = document.createElement('span');
  numberSpan.textContent = displayNumber;
  numberSpan.style.minWidth = '30px';
  numberSpan.style.height = '30px';
  numberSpan.style.display = 'flex';
  numberSpan.style.alignItems = 'center';
  numberSpan.style.justifyContent = 'center';
  numberSpan.style.backgroundColor = '#007bff';
  numberSpan.style.color = 'white';
  numberSpan.style.borderRadius = '50%';
  numberSpan.style.fontWeight = 'bold';

  // إضافة تمييز للرضع
  if (passenger.type === 'INF') {
    numberSpan.style.backgroundColor = '#dc3545';
    numberSpan.style.fontStyle = 'italic';
    numberSpan.title = `رضيع مع المسافر رقم ${displayNumber}`;
  }

  // مربع الاسم
  const nameInput = document.createElement('input');
  nameInput.type = 'text';
  nameInput.className = 'name-input';
  nameInput.value = passenger.name;
  nameInput.style.flex = '2';
  nameInput.style.padding = '8px';
  nameInput.style.fontSize = '14px';
  nameInput.style.border = '2px solid #ddd';
  nameInput.style.borderRadius = '5px';
  nameInput.style.minWidth = '200px';

  // مربع نوع الراكب
  const typeInput = document.createElement('input');
  typeInput.type = 'text';
  typeInput.className = 'type-input';
  typeInput.value = passenger.type;
  typeInput.readOnly = true;
  typeInput.style.width = '80px';
  typeInput.style.padding = '8px';
  typeInput.style.fontSize = '14px';
  typeInput.style.textAlign = 'center';
  typeInput.style.fontWeight = 'bold';
  typeInput.style.borderRadius = '5px';

  // تحديد ألوان حسب نوع الراكب
  let bgColor, borderColor, textColor;
  if (passenger.type === 'CHD') {
    bgColor = '#fff3cd'; // أصفر فاتح للأطفال
    borderColor = '#ffeaa7';
    textColor = '#856404';
  } else if (passenger.type === 'INF') {
    bgColor = '#f8d7da'; // وردي فاتح للرضع
    borderColor = '#f5c6cb';
    textColor = '#721c24';
  } else {
    bgColor = '#d4edda'; // أخضر فاتح للبالغين
    borderColor = '#c3e6cb';
    textColor = '#155724';
  }

  typeInput.style.backgroundColor = bgColor;
  typeInput.style.border = '2px solid ' + borderColor;
  typeInput.style.color = textColor;

  // زر النسخ
  const copyButton = document.createElement('button');
  copyButton.textContent = 'نسخ';
  copyButton.className = 'copy-btn';
  copyButton.style.padding = '8px 15px';
  copyButton.style.fontSize = '14px';
  copyButton.style.backgroundColor = '#007bff';
  copyButton.style.color = 'white';
  copyButton.style.border = 'none';
  copyButton.style.borderRadius = '5px';
  copyButton.style.cursor = 'pointer';
  copyButton.onclick = () => copyPassengerToClipboard(passenger, displayNumber);

  // إضافة العناصر للصف
  detailsRow.appendChild(numberSpan);
  detailsRow.appendChild(nameInput);
  detailsRow.appendChild(typeInput);
  detailsRow.appendChild(copyButton);

  container.appendChild(detailsRow);

  // إضافة نموذج معلومات الهوية
  const identitySection = document.createElement('div');
  identitySection.className = 'identity-section';
  identitySection.style.marginTop = '20px';
  identitySection.style.padding = '15px';
  identitySection.style.backgroundColor = '#ffffff';
  identitySection.style.border = '2px solid #007bff';
  identitySection.style.borderRadius = '8px';

  // عنوان قسم الهوية
  const identityTitle = document.createElement('h5');
  identityTitle.textContent = 'إرسال معلومات هوية الراكب';
  identityTitle.style.margin = '0 0 15px 0';
  identityTitle.style.color = '#007bff';
  identityTitle.style.textAlign = 'center';
  identityTitle.style.fontSize = '16px';
  identityTitle.style.fontWeight = 'bold';
  identitySection.appendChild(identityTitle);

  // إنشاء نموذج الهوية
  createIdentityForm(identitySection, passenger, displayNumber);

  container.appendChild(identitySection);
  container.style.display = 'block';
}

// دالة إنشاء مجموعة نموذج
function createFormGroup(labelText) {
  const group = document.createElement('div');
  group.className = 'form-group';
  group.style.marginBottom = '15px';

  const label = document.createElement('label');
  label.textContent = labelText;
  label.style.display = 'block';
  label.style.marginBottom = '5px';
  label.style.fontWeight = 'bold';
  label.style.color = '#495057';
  label.style.fontSize = '13px';

  group.appendChild(label);
  return group;
}

// دالة إنشاء نموذج معلومات الهوية
function createIdentityForm(container, passenger, passengerNumber) {
  // إنشاء شبكة النموذج
  const formGrid = document.createElement('div');
  formGrid.className = 'identity-form-grid';
  formGrid.style.display = 'grid';
  formGrid.style.gridTemplateColumns = '1fr 1fr';
  formGrid.style.gap = '15px';
  formGrid.style.marginBottom = '20px';

  // العمود الأيسر
  const leftColumn = document.createElement('div');
  leftColumn.className = 'form-column';

  // العمود الأيمن
  const rightColumn = document.createElement('div');
  rightColumn.className = 'form-column';

  // 1. نوع الوثيقة
  const docTypeGroup = createFormGroup('نوع الوثيقة:', 'docType');
  const docTypeSelect = document.createElement('select');
  docTypeSelect.id = `docType_${passengerNumber}`;
  docTypeSelect.className = 'identity-input';

  const docTypeI = document.createElement('option');
  docTypeI.value = 'i';
  docTypeI.textContent = 'هوية وطنية (i)';
  const docTypeP = document.createElement('option');
  docTypeP.value = 'p';
  docTypeP.textContent = 'جواز سفر (p)';
  docTypeP.selected = true;

  docTypeSelect.appendChild(docTypeI);
  docTypeSelect.appendChild(docTypeP);
  docTypeSelect.addEventListener('change', function() {
    toggleExpiryDateField(passengerNumber, this.value);
  });

  docTypeGroup.appendChild(docTypeSelect);
  leftColumn.appendChild(docTypeGroup);

  // 2. مكان الإصدار
  const nationalityGroup = createFormGroup('مكان الإصدار:', 'nationality');
  const nationalityInput = document.createElement('input');
  nationalityInput.type = 'text';
  nationalityInput.id = `nationality_${passengerNumber}`;
  nationalityInput.className = 'identity-input';
  nationalityInput.value = 'SAU';
  nationalityInput.placeholder = 'مثل: SAU, USA, GBR';
  nationalityGroup.appendChild(nationalityInput);
  rightColumn.appendChild(nationalityGroup);

  // 3. رقم الهوية/جواز السفر
  const idNumberGroup = createFormGroup('رقم الهوية/جواز السفر:', 'idNumber');
  const idNumberInput = document.createElement('input');
  idNumberInput.type = 'text';
  idNumberInput.id = `idNumber_${passengerNumber}`;
  idNumberInput.className = 'identity-input';
  idNumberInput.placeholder = 'أدخل رقم الهوية أو جواز السفر';
  idNumberGroup.appendChild(idNumberInput);
  leftColumn.appendChild(idNumberGroup);

  // 4. الجنسية
  const nationality2Group = createFormGroup('الجنسية:', 'nationality2');
  const nationality2Input = document.createElement('input');
  nationality2Input.type = 'text';
  nationality2Input.id = `nationality2_${passengerNumber}`;
  nationality2Input.className = 'identity-input';
  nationality2Input.value = 'SAU';
  nationality2Input.placeholder = 'جنسية المسافر';
  nationality2Group.appendChild(nationality2Input);
  rightColumn.appendChild(nationality2Group);

  // 5. تاريخ الميلاد
  const birthDateGroup = createFormGroup('تاريخ الميلاد:', 'birthDate');
  const birthDateInput = document.createElement('input');
  birthDateInput.type = 'text';
  birthDateInput.id = `birthDate_${passengerNumber}`;
  birthDateInput.className = 'identity-input';
  birthDateInput.placeholder = '01JAN80';
  birthDateInput.maxLength = 7;
  birthDateGroup.appendChild(birthDateInput);
  leftColumn.appendChild(birthDateGroup);

  // 6. الجنس
  const genderGroup = createFormGroup('الجنس:', 'gender');
  const genderSelect = document.createElement('select');
  genderSelect.id = `gender_${passengerNumber}`;
  genderSelect.className = 'identity-input';

  const genderM = document.createElement('option');
  genderM.value = 'm';
  genderM.textContent = 'ذكر (m)';
  const genderF = document.createElement('option');
  genderF.value = 'f';
  genderF.textContent = 'أنثى (f)';

  genderSelect.appendChild(genderM);
  genderSelect.appendChild(genderF);
  genderGroup.appendChild(genderSelect);
  rightColumn.appendChild(genderGroup);

  // 7. تاريخ انتهاء الهوية (للجواز فقط)
  const expiryDateGroup = createFormGroup('تاريخ انتهاء جواز السفر:', 'expiryDate');
  expiryDateGroup.id = `expiryContainer_${passengerNumber}`;
  const expiryDateInput = document.createElement('input');
  expiryDateInput.type = 'text';
  expiryDateInput.id = `expiryDate_${passengerNumber}`;
  expiryDateInput.className = 'identity-input';
  expiryDateInput.placeholder = '01SEP26';
  expiryDateInput.maxLength = 7;
  expiryDateGroup.appendChild(expiryDateInput);
  leftColumn.appendChild(expiryDateGroup);

  // إضافة الأعمدة للشبكة
  formGrid.appendChild(leftColumn);
  formGrid.appendChild(rightColumn);
  container.appendChild(formGrid);

  // معلومات المسافر (للعرض فقط)
  const passengerInfo = document.createElement('div');
  passengerInfo.className = 'passenger-info';
  passengerInfo.style.padding = '10px';
  passengerInfo.style.backgroundColor = '#f8f9fa';
  passengerInfo.style.border = '1px solid #dee2e6';
  passengerInfo.style.borderRadius = '6px';
  passengerInfo.style.marginBottom = '15px';
  passengerInfo.style.textAlign = 'center';

  const passengerText = document.createElement('span');
  passengerText.textContent = `المسافر: ${passenger.name} (رقم ${passengerNumber})`;
  passengerText.style.fontWeight = 'bold';
  passengerText.style.color = '#495057';
  passengerText.style.fontSize = '14px';

  passengerInfo.appendChild(passengerText);
  container.appendChild(passengerInfo);

  // زر إرسال الأمر
  const generateButton = document.createElement('button');
  generateButton.textContent = 'إرسال أمر الهوية';
  generateButton.className = 'generate-identity-btn';
  generateButton.style.width = '100%';
  generateButton.style.padding = '10px';
  generateButton.style.backgroundColor = '#28a745';
  generateButton.style.color = 'white';
  generateButton.style.border = 'none';
  generateButton.style.borderRadius = '6px';
  generateButton.style.fontSize = '14px';
  generateButton.style.fontWeight = 'bold';
  generateButton.style.cursor = 'pointer';
  generateButton.style.transition = 'all 0.3s ease';

  generateButton.addEventListener('click', () => {
    generateIdentityCommand(passengerNumber);
  });

  generateButton.addEventListener('mouseenter', () => {
    generateButton.style.backgroundColor = '#218838';
    generateButton.style.transform = 'translateY(-1px)';
  });

  generateButton.addEventListener('mouseleave', () => {
    generateButton.style.backgroundColor = '#28a745';
    generateButton.style.transform = 'translateY(0)';
  });

  container.appendChild(generateButton);

  // إخفاء حقل تاريخ الانتهاء افتراضياً للجواز (p)
  toggleExpiryDateField(passengerNumber, 'p');
}

// دالة إخفاء/إظهار حقل تاريخ الانتهاء
function toggleExpiryDateField(passengerNumber, docType) {
  const expiryContainer = document.getElementById(`expiryContainer_${passengerNumber}`);

  if (expiryContainer) {
    if (docType === 'i') {
      // للهوية الوطنية - إخفاء حقل تاريخ الانتهاء
      expiryContainer.style.display = 'none';
    } else {
      // لجواز السفر - إظهار حقل تاريخ الانتهاء
      expiryContainer.style.display = 'inline';
    }
  }
}

// دالة إنشاء أمر الهوية
function generateIdentityCommand(passengerNumber) {
  // جمع البيانات من النموذج
  const docType = document.getElementById(`docType_${passengerNumber}`).value;
  const nationality = document.getElementById(`nationality_${passengerNumber}`).value;
  const idNumber = document.getElementById(`idNumber_${passengerNumber}`).value;
  const nationality2 = document.getElementById(`nationality2_${passengerNumber}`).value;
  const birthDate = document.getElementById(`birthDate_${passengerNumber}`).value;
  const gender = document.getElementById(`gender_${passengerNumber}`).value;
  const expiryDate = document.getElementById(`expiryDate_${passengerNumber}`).value;

  // التحقق من البيانات المطلوبة
  if (!idNumber || idNumber.trim() === '') {
    alert('يرجى إدخال رقم الهوية/جواز السفر');
    return;
  }

  if (!birthDate || birthDate.length !== 7) {
    alert('يرجى إدخال تاريخ الميلاد بالتنسيق الصحيح (01JAN80)');
    return;
  }

  // التحقق من تاريخ الانتهاء فقط للجواز (p)
  if (docType === 'p' && (!expiryDate || expiryDate.length !== 7)) {
    alert('يرجى إدخال تاريخ انتهاء جواز السفر بالتنسيق الصحيح (01SEP26)');
    return;
  }

  // الحصول على اسم المسافر من القائمة المنسدلة
  const dropdown = document.getElementById('namesDropdown');
  const selectedOption = dropdown.options[dropdown.selectedIndex];
  const passengerName = selectedOption.getAttribute('data-name');
  const passengerType = selectedOption.getAttribute('data-type');

  // تعديل الجنس للرضع - إضافة حرف "i" للرضيع
  let modifiedGender = gender;
  if (passengerType === 'INF') {
    modifiedGender = gender + 'i'; // إضافة "i" للرضيع (مثل: m يصبح mi، f يصبح fi)
  }

  // إنشاء الأمر حسب نوع الوثيقة
  let command;
  if (docType === 'i') {
    // للهوية الوطنية - مع علامة / فارغة لتاريخ الانتهاء
    command = `SR DOCS yy HK1-${docType}/${nationality}/${idNumber}/${nationality2}/${birthDate}/${modifiedGender}//${passengerName}/p${passengerNumber}`;
  } else {
    // لجواز السفر - مع تاريخ انتهاء
    command = `SR DOCS yy HK1-${docType}/${nationality}/${idNumber}/${nationality2}/${birthDate}/${modifiedGender}/${expiryDate}/${passengerName}/p${passengerNumber}`;
  }

  console.log('أمر الهوية المُنشأ:', command);
  console.log('نوع المسافر:', passengerType);
  console.log('الجنس الأصلي:', gender);
  console.log('الجنس المعدل:', modifiedGender);

  // إرسال الأمر للصفحة والضغط على Enter
  sendIdentityCommandToPage(command);
}

// دالة إرسال أمر الهوية للصفحة
async function sendIdentityCommandToPage(command) {
  try {
    // الحصول على التبويب النشط
    const tab = await getCurrentTab();

    if (!tab) {
      alert('لا يمكن العثور على التبويب النشط');
      return;
    }

    console.log('إرسال أمر الهوية للصفحة:', command);

    // الحصول على الإعدادات المحفوظة للحقل المخصص
    const result = await chrome.storage.sync.get([
      'customInputSelector'
    ]);

    // استخدام الحقل المخصص أو الافتراضي
    const inputSelector = result.customInputSelector || '#tpl0_shellbridge_shellWindow_top_left_modeString_cmdPromptInput';

    // إرسال الأمر باستخدام نفس آلية الأوامر المخصصة
    await sendCommandWithEnter(tab.id, inputSelector, command);

    // إظهار رسالة نجاح
    showSuccessMessage('تم إرسال أمر الهوية بنجاح!');

    // إظهار الأمر في منطقة منفصلة
    displayGeneratedCommand(command);

    console.log('تم إرسال أمر الهوية بنجاح');

  } catch (error) {
    console.error('خطأ في إرسال أمر الهوية:', error);

    // في حالة الفشل، محاولة النسخ للحافظة كبديل
    try {
      await navigator.clipboard.writeText(command);
      showSuccessMessage('تم نسخ أمر الهوية للحافظة (فشل الإرسال التلقائي)');
      displayGeneratedCommand(command);
    } catch (clipboardError) {
      console.error('خطأ في نسخ الأمر للحافظة:', clipboardError);
      alert('فشل في إرسال الأمر ونسخه للحافظة');
    }
  }
}

// دالة إظهار رسالة النجاح
function showSuccessMessage(message) {
  // إنشاء عنصر الرسالة
  const messageDiv = document.createElement('div');
  messageDiv.textContent = message;
  messageDiv.style.position = 'fixed';
  messageDiv.style.top = '20px';
  messageDiv.style.right = '20px';
  messageDiv.style.backgroundColor = '#28a745';
  messageDiv.style.color = 'white';
  messageDiv.style.padding = '10px 15px';
  messageDiv.style.borderRadius = '6px';
  messageDiv.style.fontSize = '14px';
  messageDiv.style.fontWeight = 'bold';
  messageDiv.style.zIndex = '10000';
  messageDiv.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
  messageDiv.style.transition = 'all 0.3s ease';

  document.body.appendChild(messageDiv);

  // إزالة الرسالة بعد 3 ثوان
  setTimeout(() => {
    messageDiv.style.opacity = '0';
    messageDiv.style.transform = 'translateY(-20px)';
    setTimeout(() => {
      document.body.removeChild(messageDiv);
    }, 300);
  }, 3000);
}

// دالة عرض الأمر المُنشأ
function displayGeneratedCommand(command) {
  // البحث عن منطقة عرض الأوامر أو إنشاؤها
  let commandDisplay = document.getElementById('generatedCommandDisplay');

  if (!commandDisplay) {
    commandDisplay = document.createElement('div');
    commandDisplay.id = 'generatedCommandDisplay';
    commandDisplay.style.marginTop = '15px';
    commandDisplay.style.padding = '15px';
    commandDisplay.style.backgroundColor = '#f8f9fa';
    commandDisplay.style.border = '2px solid #28a745';
    commandDisplay.style.borderRadius = '8px';

    const title = document.createElement('h6');
    title.textContent = 'الأمر المُنشأ:';
    title.style.margin = '0 0 10px 0';
    title.style.color = '#28a745';
    title.style.fontWeight = 'bold';

    const commandText = document.createElement('div');
    commandText.id = 'commandText';
    commandText.style.fontFamily = 'monospace';
    commandText.style.fontSize = '13px';
    commandText.style.backgroundColor = '#ffffff';
    commandText.style.padding = '10px';
    commandText.style.border = '1px solid #ddd';
    commandText.style.borderRadius = '4px';
    commandText.style.wordBreak = 'break-all';
    commandText.style.color = '#495057';

    commandDisplay.appendChild(title);
    commandDisplay.appendChild(commandText);

    // إضافة منطقة العرض إلى الحاوية الرئيسية
    const passengerDetails = document.getElementById('passengerDetails');
    passengerDetails.appendChild(commandDisplay);
  }

  // تحديث النص
  const commandText = document.getElementById('commandText');
  commandText.textContent = command;
}

function copyPassengerToClipboard(passenger, number) {
  const fullText = `${number}. ${passenger.name} (${passenger.type})`;
  navigator.clipboard.writeText(fullText).then(() => {
    showStatus(`تم نسخ بيانات المسافر ${number}`, 'success');
  }).catch(error => {
    console.error('فشل في نسخ بيانات المسافر:', error);
    showStatus('فشل في نسخ البيانات', 'error');
  });
}

// معالج مسح النتائج
document.getElementById('clearResult').addEventListener('click', () => {
  document.getElementById('result').value = '';
  showStatus('تم مسح النتائج', 'info');
});

// معالج نسخ جميع الأسماء
document.getElementById('copyAllNames').addEventListener('click', async () => {
  try {
    const nameInputs = document.querySelectorAll('.name-input');
    const names = Array.from(nameInputs).map(input => input.value).filter(name => name.trim());

    if (names.length === 0) {
      showStatus('لا توجد أسماء للنسخ', 'warning');
      return;
    }

    const namesText = names.join('\n');
    await navigator.clipboard.writeText(namesText);
    showStatus(`تم نسخ ${names.length} اسم`, 'success');

  } catch (error) {
    logger.error('فشل في نسخ الأسماء', error);
    showStatus('فشل في نسخ الأسماء', 'error');
  }
});

// معالج مسح الأسماء
document.getElementById('clearNames').addEventListener('click', () => {
  const namesSection = document.getElementById('namesSection');
  const namesContainer = document.getElementById('namesContainer');
  const namesCount = document.getElementById('namesCount');

  namesContainer.innerHTML = '';
  namesCount.textContent = '0 اسم';
  namesSection.style.display = 'none';

  showStatus('تم مسح جميع الأسماء', 'info');
});

// دالة نسخ اسم واحد
window.copyNameToClipboard = async function(button) {
  try {
    const nameInput = button.parentElement.querySelector('.name-input');
    const name = nameInput.value.trim();

    if (!name) {
      showStatus('الاسم فارغ', 'warning');
      return;
    }

    await navigator.clipboard.writeText(name);

    // تأثير بصري للنسخ
    const originalText = button.textContent;
    button.textContent = '✓';
    button.style.background = '#28a745';
    button.style.color = 'white';

    setTimeout(() => {
      button.textContent = originalText;
      button.style.background = '#e9ecef';
      button.style.color = '#495057';
    }, 1000);

  } catch (error) {
    logger.error('فشل في نسخ الاسم', error);
    showStatus('فشل في نسخ الاسم', 'error');
  }
};

// معالج فتح الإعدادات - معطل في الواجهة المبسطة
function openOptionsPage() {
  try {
    chrome.runtime.openOptionsPage();
    logger.log('تم فتح صفحة الإعدادات');
  } catch (error) {
    logger.error('فشل في فتح صفحة الإعدادات', error);
  }
}

// دالة جلب الأسماء تلقائياً
async function autoFetchNames() {
  try {
    // التحقق من إعداد الجلب التلقائي
    const settings = await chrome.storage.sync.get('autoFetchNames');
    const autoFetchEnabled = settings.autoFetchNames !== false; // افتراضياً مفعل

    if (!autoFetchEnabled) {
      console.log('الجلب التلقائي للأسماء معطل في الإعدادات');
      return;
    }

    console.log('بدء جلب الأسماء تلقائياً...');
    showStatus('جاري جلب الأسماء تلقائياً...', 'info');

    // الحصول على المحدد المخصص للمخرجات
    const result = await chrome.storage.sync.get('customOutputSelector');
    const selector = result.customOutputSelector || '[id*="cmdResponse"][id*="_speedMode"] > pre, [id*="cmdResponse"][id*="_speedModeTarget"]';

    // جلب النص من المخرجات
    const tab = await getCurrentTab();
    const response = await sendMessageToTab(tab.id, {
      action: 'sendCommand',
      command: 'getText',
      selector: selector
    });

    if (response.success && response.result?.text) {
      console.log('تم جلب النص تلقائياً من المخرجات');

      // استخراج الأسماء من النص
      const passengers = extractNamesFromText(response.result.text);

      if (passengers.length > 0) {
        displayNamesInTextBoxes(passengers);
        showStatus(`تم جلب ${passengers.length} مسافر تلقائياً`, 'success');
        console.log(`تم جلب ${passengers.length} مسافر تلقائياً`);
      } else {
        showStatus('لم يتم العثور على أسماء في الصفحة', 'warning');
        console.log('لم يتم العثور على أسماء في النص المجلوب تلقائياً');
      }
    } else {
      console.log('لم يتم العثور على نص في المخرجات - الجلب التلقائي فشل بصمت');
      // لا نظهر رسالة خطأ للمستخدم لأن هذا جلب تلقائي
    }

  } catch (error) {
    console.log('فشل الجلب التلقائي للأسماء:', error.message);
    // لا نظهر رسالة خطأ للمستخدم لأن هذا جلب تلقائي
  }
}

// تهيئة الواجهة عند تحميل الصفحة - الواجهة المبسطة
document.addEventListener('DOMContentLoaded', async () => {
  // initTabs(); // معطل في الواجهة المبسطة
  initCommandBuilder(); // محفوظ للوظائف الداخلية
  loadSettings();

  // تحميل إعداد الجلب التلقائي
  loadAutoFetchSetting();

  // جلب الأسماء تلقائياً بعد تحميل الصفحة
  setTimeout(autoFetchNames, 1000); // انتظار ثانية واحدة للتأكد من تحميل الصفحة

  // إعداد النافذة المنفصلة
  setupWindowControls();

  // إضافة أنماط CSS للحالات
  const style = document.createElement('style');
  style.textContent = `
    .status {
      border-radius: 6px;
      padding: 10px;
      margin: 5px 0;
      font-size: 12px;
      font-weight: 500;
    }
    .status.success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .status.error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .status.warning {
      background-color: #fff3cd;
      color: #856404;
      border: 1px solid #ffeaa7;
    }
    .status.info {
      background-color: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }
  `;
  document.head.appendChild(style);
});

// تحميل الإعدادات المحفوظة
async function loadSettings() {
  try {
    const response = await chrome.runtime.sendMessage({action: 'getSettings'});
    if (response.success && response.settings) {
      // تطبيق الإعدادات على الواجهة
      const settings = response.settings;
      if (settings.defaultTimeout) {
        document.getElementById('waitTime').value = settings.defaultTimeout;
      }
    }
  } catch (error) {
    logger.error('فشل في تحميل الإعدادات', error);
  }
}

// إعداد النافذة المنفصلة
function setupWindowControls() {
  // التحقق من وضع النافذة المنفصلة
  const isStandalone = new URLSearchParams(window.location.search).get('standalone') === 'true';

  if (isStandalone) {
    // تطبيق CSS class للنافذة المنفصلة
    document.body.classList.add('standalone');

    // إخفاء زر "فتح في نافذة منفصلة"
    const openInWindowBtn = document.getElementById('openInWindow');
    if (openInWindowBtn) {
      openInWindowBtn.style.display = 'none';
    }

    // إضافة عنوان للنافذة المنفصلة
    const header = document.querySelector('.header h2');
    if (header) {
      header.textContent = 'أتمتة المواقع - نافذة منفصلة';
    }

    // إضافة معلومات إضافية للنافذة المنفصلة
    const headerDesc = document.querySelector('.header p');
    if (headerDesc) {
      headerDesc.textContent = 'نافذة منفصلة - يمكن تحريكها وتغيير حجمها';
    }

    console.log('تم تفعيل وضع النافذة المنفصلة');
  } else {
    // إضافة معالج الحدث لزر فتح النافذة المنفصلة
    const openInWindowBtn = document.getElementById('openInWindow');
    if (openInWindowBtn) {
      openInWindowBtn.addEventListener('click', openInSeparateWindow);

      // إضافة تأثير hover
      openInWindowBtn.addEventListener('mouseenter', () => {
        openInWindowBtn.style.background = '#5a6268';
      });

      openInWindowBtn.addEventListener('mouseleave', () => {
        openInWindowBtn.style.background = '#6c757d';
      });
    }
  }
}

// دالة فتح النافذة المنفصلة
async function openInSeparateWindow() {
  try {
    // الحصول على التبويب النشط الحالي لحفظ معرفه
    const currentTab = await getCurrentTab();
    const currentUrl = chrome.runtime.getURL('popup.html') + `?standalone=true&targetTab=${currentTab.id}`;

    // إنشاء النافذة المنفصلة
    await chrome.windows.create({
      url: currentUrl,
      type: 'popup',
      width: 520,
      height: 720,
      focused: true,
      left: Math.round((screen.width - 520) / 2),
      top: Math.round((screen.height - 720) / 2)
    });

    // إغلاق النافذة المنبثقة الحالية
    window.close();

  } catch (error) {
    console.error('فشل في فتح النافذة المنفصلة:', error);
    showStatus('فشل في فتح النافذة المنفصلة', 'error');
  }
}

// تحميل إعداد الجلب التلقائي
async function loadAutoFetchSetting() {
  try {
    const result = await chrome.storage.sync.get('autoFetchNames');
    const autoFetchEnabled = result.autoFetchNames !== false; // افتراضياً مفعل

    const checkbox = document.getElementById('autoFetchToggle');
    if (checkbox) {
      checkbox.checked = autoFetchEnabled;

      // إضافة معالج الحدث للتحكم في الإعداد
      checkbox.addEventListener('change', async (e) => {
        const enabled = e.target.checked;
        await chrome.storage.sync.set({ autoFetchNames: enabled });

        const statusMessage = enabled ? 'تم تفعيل الجلب التلقائي' : 'تم تعطيل الجلب التلقائي';
        showStatus(statusMessage, 'info');
        console.log(statusMessage);
      });
    }
  } catch (error) {
    console.error('فشل في تحميل إعداد الجلب التلقائي:', error);
  }
}

// حفظ الإعدادات
async function saveCurrentSettings() {
  try {
    const settings = {
      defaultTimeout: parseInt(document.getElementById('waitTime').value) || 5000
    };

    await chrome.runtime.sendMessage({
      action: 'saveSettings',
      settings: settings
    });

    showStatus('تم حفظ الإعدادات', 'success');
  } catch (error) {
    logger.error('فشل في حفظ الإعدادات', error);
  }
}

// إضافة اختصارات لوحة المفاتيح
document.addEventListener('keydown', (e) => {
  // Ctrl+Enter لتنفيذ الأمر
  if (e.ctrlKey && e.key === 'Enter') {
    e.preventDefault();
    document.getElementById('sendCommand').click();
  }

  // Ctrl+1 للتبويب الأساسي
  if (e.ctrlKey && e.key === '1') {
    e.preventDefault();
    document.querySelector('[data-tab="basic"]').click();
  }

  // Ctrl+2 للتبويب المتقدم
  if (e.ctrlKey && e.key === '2') {
    e.preventDefault();
    document.querySelector('[data-tab="advanced"]').click();
  }

  // Escape لمسح النتائج
  if (e.key === 'Escape') {
    e.preventDefault();
    document.getElementById('clearResult').click();
  }
});

// إضافة tooltips للأزرار
function addTooltips() {
  const tooltips = {
    'fetchNames': 'جلب جميع الأسماء من الصفحة باستخدام المحدد المحفوظ',
    'quickClick': 'نقر سريع على عنصر محدد',
    'quickType': 'كتابة نص في حقل إدخال + ضغط Enter',
    'quickWait': 'انتظار ظهور عنصر في الصفحة',
    'quickGet': 'جلب نص من عنصر محدد',
    'customInput': 'كتابة نص في الحقل المخصص + ضغط Enter',
    'customOutput': 'جلب النص من المخرجات المخصصة',
    'extractNames': 'جلب الأسماء من المخرجات وعرضها في مربعات نص منفصلة',
    'copyAllNames': 'نسخ جميع الأسماء إلى الحافظة',
    'clearNames': 'مسح جميع الأسماء المعروضة',
    'sendCommand': 'تنفيذ أمر مخصص (Ctrl+Enter)',
    'clearResult': 'مسح جميع النتائج (Escape)',
    'openOptions': 'فتح صفحة الإعدادات المتقدمة'
  };

  Object.entries(tooltips).forEach(([id, tooltip]) => {
    const element = document.getElementById(id);
    if (element) {
      element.title = tooltip;
    }
  });
}

// تشغيل إضافة tooltips عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', addTooltips);