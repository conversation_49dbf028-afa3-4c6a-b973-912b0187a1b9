{"manifest_version": 3, "name": "Web Automator", "version": "2.0", "description": "إضافة متقدمة لأتمتة المواقع واستخراج البيانات وتنفيذ السكريبتات المخصصة", "author": "Web Automator Team", "homepage_url": "https://github.com/web-automator/extension", "permissions": ["activeTab", "scripting", "storage", "tabs", "notifications", "contextMenus"], "host_permissions": ["<all_urls>"], "action": {"default_popup": "popup.html", "default_title": "Web Automator - أتمتة المواقع"}, "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_end", "all_frames": false}], "options_page": "options.html", "commands": {"toggle-automator": {"suggested_key": {"default": "Ctrl+Shift+A", "mac": "Command+Shift+A"}, "description": "فتح/إغلاق Web Automator"}, "quick-extract": {"suggested_key": {"default": "Ctrl+Shift+E", "mac": "Command+Shift+E"}, "description": "استخراج سريع للبيانات"}}, "minimum_chrome_version": "88", "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'"}}