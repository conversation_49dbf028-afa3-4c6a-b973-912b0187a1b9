<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Web Automator</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      width: 400px;
      min-height: 450px;
      padding: 12px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #333;
      direction: rtl;
    }

    .container {
      background: white;
      border-radius: 12px;
      padding: 18px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(10px);
    }

    .header {
      text-align: center;
      margin-bottom: 15px;
      padding-bottom: 12px;
      border-bottom: 2px solid #f0f0f0;
    }

    .header h2 {
      color: #2c3e50;
      font-size: 20px;
      margin-bottom: 5px;
    }

    /* تحسينات للواجهة المبسطة */
    .simplified-interface {
      margin-top: 0;
    }

    .simplified-interface .section {
      margin-bottom: 20px;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;
    }

    .simplified-interface .section-title {
      font-size: 16px;
      font-weight: bold;
      color: #495057;
      margin-bottom: 12px;
      text-align: center;
      padding-bottom: 8px;
      border-bottom: 2px solid #007bff;
    }

    .simplified-interface .btn {
      width: 100%;
      padding: 12px;
      font-size: 14px;
      font-weight: 600;
      border-radius: 8px;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .simplified-interface .btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .simplified-interface .phone-section {
      background: white;
      padding: 15px;
      border-radius: 8px;
      border: 2px solid #e9ecef;
    }

    .header p {
      color: #7f8c8d;
      font-size: 12px;
    }

    .section {
      margin-bottom: 20px;
    }

    .section-title {
      font-size: 14px;
      font-weight: bold;
      color: #34495e;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
    }

    .section-title::before {
      content: '';
      width: 4px;
      height: 16px;
      background: #3498db;
      margin-left: 8px;
      border-radius: 2px;
    }

    .button-group {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .btn {
      padding: 12px 16px;
      border: none;
      border-radius: 8px;
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      text-align: center;
    }

    .btn:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .btn:active:not(:disabled) {
      transform: translateY(0);
    }

    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none !important;
    }

    .btn-primary {
      background: linear-gradient(135deg, #3498db, #2980b9);
      color: white;
    }

    .btn-secondary {
      background: linear-gradient(135deg, #95a5a6, #7f8c8d);
      color: white;
    }

    .btn-success {
      background: linear-gradient(135deg, #2ecc71, #27ae60);
      color: white;
    }

    .btn-warning {
      background: linear-gradient(135deg, #f39c12, #e67e22);
      color: white;
    }

    .input-group {
      margin-bottom: 15px;
    }

    .input-group label {
      display: block;
      font-size: 12px;
      font-weight: 500;
      color: #555;
      margin-bottom: 5px;
    }

    .input-group input,
    .input-group select,
    .input-group textarea {
      width: 100%;
      padding: 10px;
      border: 2px solid #e0e0e0;
      border-radius: 6px;
      font-size: 13px;
      transition: border-color 0.3s ease;
      font-family: inherit;
    }

    .input-group input:focus,
    .input-group select:focus,
    .input-group textarea:focus {
      outline: none;
      border-color: #3498db;
    }

    .result-area {
      background: #f8f9fa;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      padding: 12px;
      min-height: 100px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      line-height: 1.4;
      resize: vertical;
      direction: ltr;
      text-align: left;
    }

    .tabs {
      display: flex;
      border-bottom: 2px solid #f0f0f0;
      margin-bottom: 15px;
    }

    .tab {
      flex: 1;
      padding: 10px;
      text-align: center;
      cursor: pointer;
      font-size: 12px;
      font-weight: 500;
      color: #7f8c8d;
      border-bottom: 2px solid transparent;
      transition: all 0.3s ease;
    }

    .tab.active {
      color: #3498db;
      border-bottom-color: #3498db;
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    .command-builder {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 15px;
    }

    .quick-actions {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
      margin-bottom: 15px;
    }

    .quick-action {
      padding: 8px;
      font-size: 11px;
      border-radius: 6px;
    }

    .footer {
      text-align: center;
      margin-top: 20px;
      padding-top: 15px;
      border-top: 1px solid #f0f0f0;
    }

    .footer .btn {
      font-size: 11px;
      padding: 8px 12px;
    }

    /* أيقونات بسيطة */
    .icon::before {
      margin-left: 5px;
    }

    .icon-fetch::before { content: '📋'; }
    .icon-send::before { content: '🚀'; }
    .icon-wait::before { content: '⏳'; }
    .icon-click::before { content: '👆'; }
    .icon-type::before { content: '⌨️'; }
    .icon-get::before { content: '📄'; }
    .icon-settings::before { content: '⚙️'; }
    .icon-clear::before { content: '🗑️'; }

    /* تنسيق منطقة الأسماء */
    .names-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      padding: 8px;
      background: #f8f9fa;
      border-radius: 6px;
    }

    .names-count {
      font-weight: bold;
      color: #495057;
      font-size: 0.9em;
    }

    .names-container {
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      padding: 10px;
      background: #ffffff;
    }

    /* تنسيق قسم رقم الجوال */
    .phone-section {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .phone-input-container {
      display: flex;
      align-items: center;
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      overflow: hidden;
      background: white;
      transition: border-color 0.3s ease;
    }

    .phone-input-container:focus-within {
      border-color: #4CAF50;
      box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
    }

    .country-code {
      background: #f5f5f5;
      padding: 12px 15px;
      font-weight: 600;
      color: #333;
      border-right: 1px solid #e0e0e0;
      font-size: 14px;
    }

    #phoneInput {
      flex: 1;
      border: none;
      padding: 12px 15px;
      font-size: 14px;
      outline: none;
      background: transparent;
    }

    #phoneInput::placeholder {
      color: #999;
    }

    .input-help {
      color: #666;
      font-size: 12px;
      margin-top: 5px;
      display: block;
    }

    .auto-count-info {
      margin-top: 8px;
      padding: 8px 12px;
      background: #e3f2fd;
      border: 1px solid #bbdefb;
      border-radius: 4px;
    }

    .info-text {
      font-size: 12px;
      color: #1976d2;
      margin: 0;
      display: block;
      font-weight: 500;
    }

    .icon-phone::before {
      content: "📱";
    }

    /* تنسيق القائمة المنسدلة */
    .names-dropdown {
      width: 100%;
      padding: 12px;
      font-size: 14px;
      border: 2px solid #ddd;
      border-radius: 8px;
      background: white;
      color: #333;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-bottom: 15px;
    }

    .names-dropdown:hover {
      border-color: #007bff;
      box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
    }

    .names-dropdown:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }

    /* تنسيق منطقة تفاصيل المسافر */
    .passenger-details {
      background: #f8f9fa;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      padding: 15px;
      margin-top: 15px;
      transition: all 0.3s ease;
    }

    .passenger-details h4 {
      color: #495057;
      margin-bottom: 15px;
      font-size: 16px;
      text-align: center;
    }

    .name-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      gap: 8px;
    }

    .name-input {
      flex: 1;
      padding: 8px 12px;
      border: 2px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
      background: #fff;
      transition: all 0.3s ease;
      min-width: 200px;
    }

    .name-input:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }

    .type-input {
      width: 80px;
      padding: 8px 12px;
      border: 2px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
      text-align: center;
      font-weight: bold;
      transition: all 0.3s ease;
    }

    .copy-btn {
      padding: 8px 15px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .copy-btn:hover {
      background: #0056b3;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
    }

    /* تنسيق نموذج الهوية */
    .identity-section {
      background: #ffffff;
      border: 2px solid #007bff;
      border-radius: 8px;
      padding: 20px;
      margin-top: 20px;
    }

    .identity-form-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-bottom: 20px;
    }

    .form-column {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-group label {
      margin-bottom: 5px;
      font-weight: bold;
      color: #495057;
      font-size: 13px;
    }

    .identity-input {
      padding: 10px 12px;
      border: 2px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
      transition: all 0.3s ease;
      width: 100%;
      box-sizing: border-box;
    }

    .identity-input:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }

    .passenger-info {
      padding: 12px;
      background: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 6px;
      margin-bottom: 15px;
      text-align: center;
      font-weight: bold;
      color: #495057;
    }

    @media (max-width: 600px) {
      .identity-form-grid {
        grid-template-columns: 1fr;
        gap: 15px;
      }
    }

    .generate-identity-btn {
      width: 100%;
      padding: 12px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 6px;
      font-size: 15px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-top: 10px;
    }

    .generate-identity-btn:hover {
      background: #0056b3;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
    }

    #expiryContainer_1, #expiryContainer_2, #expiryContainer_3, #expiryContainer_4, #expiryContainer_5 {
      transition: all 0.3s ease;
    }

    #generatedCommandDisplay {
      margin-top: 15px;
      padding: 15px;
      background: #f8f9fa;
      border: 2px solid #28a745;
      border-radius: 8px;
    }

    #commandText {
      font-family: 'Courier New', monospace;
      font-size: 13px;
      background: #ffffff;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      word-break: break-all;
      color: #495057;
      line-height: 1.4;
    }

    .name-number {
      min-width: 30px;
      text-align: center;
      font-weight: bold;
      color: #6c757d;
      font-size: 0.8em;
    }

    .name-copy {
      padding: 4px 8px;
      font-size: 0.8em;
      border: none;
      background: #e9ecef;
      color: #495057;
      border-radius: 3px;
      cursor: pointer;
      transition: background-color 0.2s;
    }

    .name-copy:hover {
      background: #dee2e6;
    }

    .btn-sm {
      padding: 4px 12px;
      font-size: 0.85em;
    }

    /* تحسينات للنافذة المنفصلة */
    .window-controls {
      border-top: 1px solid #e9ecef;
      padding-top: 10px;
      margin-top: 15px;
    }

    .window-controls .btn {
      transition: all 0.2s ease;
    }

    .window-controls .btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* تعديلات للنافذة المنفصلة */
    body.standalone {
      width: 480px !important;
      min-height: 680px !important;
      padding: 15px !important;
    }

    body.standalone .container {
      box-shadow: none;
      border: 1px solid #dee2e6;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h2>أتمتة المواقع</h2>
      <p>أداة قوية لأتمتة التفاعل مع صفحات الويب</p>
    </div>

    <!-- التبويبات مخفية -->
    <div class="tabs" style="display: none;">
      <div class="tab active" data-tab="basic">العمليات الأساسية</div>
      <div class="tab" data-tab="advanced">متقدم</div>
    </div>

    <!-- التبويب الأساسي -->
    <div class="tab-content active" id="basic">
      <!-- الواجهة المبسطة - العناصر المطلوبة فقط -->
      <div class="section">
        <div class="section-title">جلب الأسماء</div>
        <div class="button-group">
          <button id="extractNames" class="btn btn-success icon icon-fetch">جلب الأسماء في مربعات</button>
          <button id="refreshNames" class="btn btn-info icon icon-refresh" style="margin-top: 8px; font-size: 12px; padding: 8px;">تحديث الأسماء</button>
        </div>
        <div class="auto-fetch-controls" style="margin-top: 10px; text-align: center;">
          <label style="font-size: 12px; color: #6c757d;">
            <input type="checkbox" id="autoFetchToggle" checked>
            جلب الأسماء تلقائياً عند فتح الإضافة
          </label>
        </div>
      </div>

      <!-- العناصر المخفية - محفوظة للوظائف الداخلية -->
      <div style="display: none;">
        <div class="section">
          <div class="section-title">استخراج البيانات</div>
          <div class="button-group">
            <button id="fetchNames" class="btn btn-primary icon icon-fetch">جلب الأسماء من الصفحة</button>
          </div>
        </div>

        <div class="section">
          <div class="section-title">الإجراءات السريعة</div>
          <div class="quick-actions">
            <button id="quickClick" class="btn btn-success quick-action icon icon-click">نقرة سريعة</button>
            <button id="quickType" class="btn btn-warning quick-action icon icon-type">كتابة سريعة</button>
            <button id="quickWait" class="btn btn-secondary quick-action icon icon-wait">انتظار عنصر</button>
            <button id="quickGet" class="btn btn-primary quick-action icon icon-get">جلب نص</button>
          </div>
        </div>

        <div class="section">
          <div class="section-title">أوامر مخصصة</div>
          <div class="quick-actions">
            <button id="customInput" class="btn btn-info quick-action icon icon-type">كتابة في الحقل المخصص</button>
            <button id="customOutput" class="btn btn-primary quick-action icon icon-get">جلب من المخرجات</button>
          </div>
        </div>
      </div>

      <!-- إدخال رقم الجوال -->
      <div class="section">
        <div class="section-title">تسجيل رقم الجوال</div>
        <div class="phone-section">
          <div class="input-group">
            <label for="phoneInput">رقم الجوال:</label>
            <div class="phone-input-container">
              <span class="country-code">+966</span>
              <input type="tel" id="phoneInput" placeholder="5xxxxxxxx" maxlength="9" pattern="[0-9]{9}">
            </div>
            <small class="input-help">أدخل رقم الجوال بدون رمز الدولة (9 أرقام)</small>
            <div class="auto-count-info">
              <small class="info-text">💡 سيتم حساب عدد المسافرين تلقائياً من الأسماء المستخرجة</small>
            </div>
          </div>
          <div class="quick-actions">
            <button id="registerPhone" class="btn btn-primary quick-action icon icon-phone">
              تسجيل رقم الجوال
            </button>
          </div>
        </div>
      </div>

      <!-- منطقة عرض الأسماء في مربعات نص -->
      <div class="section" id="namesSection" style="display: none;">
        <div class="section-title">الأسماء المستخرجة</div>
        <div class="names-controls">
          <button id="copyAllNames" class="btn btn-secondary btn-sm">نسخ جميع الأسماء</button>
          <button id="clearNames" class="btn btn-warning btn-sm">مسح الأسماء</button>
          <span id="namesCount" class="names-count">0 اسم</span>
        </div>
        <div id="namesContainer" class="names-container">
          <!-- مربعات النص ستظهر هنا -->
        </div>
      </div>

      <div class="section">
        <div class="section-title">النتائج</div>
        <textarea id="result" class="result-area" readonly placeholder="ستظهر النتائج هنا..."></textarea>
        <button id="clearResult" class="btn btn-secondary icon icon-clear" style="margin-top: 8px; font-size: 11px; padding: 6px 10px;">مسح النتائج</button>
      </div>
    </div>

    <!-- التبويب المتقدم مخفي -->
    <div class="tab-content" id="advanced" style="display: none;">
      <div class="section">
        <div class="section-title">منشئ الأوامر</div>
        <div class="command-builder">
          <div class="input-group">
            <label>نوع الأمر:</label>
            <select id="commandType">
              <option value="input">إدخال نص</option>
              <option value="inputWithEnter">إدخال نص + Enter</option>
              <option value="click">نقر</option>
              <option value="getText">جلب النص</option>
              <option value="getAttribute">جلب خاصية</option>
            </select>
          </div>

          <div class="input-group">
            <label>محدد العنصر (CSS Selector):</label>
            <input type="text" id="elementSelector" placeholder="#myInput, .button-class, [data-id='123']">
          </div>

          <div class="input-group" id="valueGroup">
            <label>القيمة:</label>
            <input type="text" id="inputValue" placeholder="النص المراد إدخاله">
          </div>

          <div class="input-group" id="attributeGroup" style="display: none;">
            <label>اسم الخاصية:</label>
            <input type="text" id="attributeName" placeholder="href, src, data-value">
          </div>

          <div class="input-group">
            <label>وقت الانتظار (بالميلي ثانية):</label>
            <input type="number" id="waitTime" value="0" min="0" max="10000">
          </div>

          <button id="sendCommand" class="btn btn-primary icon icon-send">تنفيذ الأمر</button>
        </div>
      </div>
    </div>

    <!-- الإعدادات مخفية -->
    <div class="footer" style="display: none;">
      <button id="openOptions" class="btn btn-secondary icon icon-settings">إعدادات الإضافة</button>
    </div>

    <!-- خيارات النافذة -->
    <div class="window-controls" style="text-align: center; margin-top: 15px; padding-top: 10px; border-top: 1px solid #e9ecef;">
      <button id="openInWindow" class="btn btn-secondary btn-sm" style="font-size: 11px; padding: 6px 12px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; transition: background 0.2s;">
        🪟 فتح في نافذة منفصلة
      </button>
    </div>
  </div>

  <script src="popup.js"></script>
</body>
</html>