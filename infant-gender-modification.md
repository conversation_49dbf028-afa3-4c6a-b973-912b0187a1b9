# تعديل الجنس للرضع في أوامر الهوية

## التعديل المطلوب:
إضافة حرف "i" لحقل الجنس عندما يكون المسافر رضيعاً (INF).

## الأمثلة:

### للمسافر البالغ أو الطفل:
```
SR DOCS yy HK1-i/SAU/1133832657/SAU/10dec24/m//ALANAZI/SAMI/p1
```

### للمسافر الرضيع:
```
SR DOCS yy HK1-i/SAU/1133832657/SAU/10dec24/mi//ALANAZI/SAMI/p1
```

## التفاصيل التقنية:

### الكود المضاف:
```javascript
// تعديل الجنس للرضع - إضافة حرف "i" للرضيع
let modifiedGender = gender;
if (passengerType === 'INF') {
  modifiedGender = gender + 'i'; // إضافة "i" للرضيع (مثل: m يصبح mi، f يصبح fi)
}
```

### الموقع في الكود:
- **الملف**: `popup.js`
- **الدالة**: `generateIdentityCommand(passengerNumber)`
- **السطر**: 1143-1147

### كيف يعمل:
1. يتم الحصول على نوع المسافر من القائمة المنسدلة
2. يتم فحص إذا كان نوع المسافر هو 'INF' (رضيع)
3. إذا كان رضيعاً، يتم إضافة حرف "i" لحقل الجنس
4. يتم استخدام الجنس المعدل في إنشاء الأمر

### أمثلة التحويل:
- **ذكر بالغ/طفل**: `m` → `m`
- **ذكر رضيع**: `m` → `mi`
- **أنثى بالغة/طفل**: `f` → `f`
- **أنثى رضيع**: `f` → `fi`

### السجلات (Logs):
تم إضافة سجلات للتتبع:
```javascript
console.log('نوع المسافر:', passengerType);
console.log('الجنس الأصلي:', gender);
console.log('الجنس المعدل:', modifiedGender);
```

## الاختبار:
1. اختر مسافراً رضيعاً (INF) من القائمة المنسدلة
2. املأ نموذج معلومات الهوية
3. اختر الجنس (ذكر أو أنثى)
4. انقر على "إرسال أمر الهوية"
5. تحقق من الأمر المُنشأ في وحدة التحكم (Console)

## النتيجة المتوقعة:
- للرضع: سيظهر الجنس مع حرف "i" إضافي (mi أو fi)
- للبالغين والأطفال: سيظهر الجنس عادياً (m أو f)
