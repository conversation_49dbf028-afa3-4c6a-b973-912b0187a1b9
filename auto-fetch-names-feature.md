# ميزة الجلب التلقائي للأسماء

## الوصف:
تم إضافة ميزة جلب الأسماء تلقائياً عند فتح الإضافة، بحيث لا يحتاج المستخدم للضغط على زر "جلب الأسماء في مربعات" في كل مرة.

## الميزات المضافة:

### 1. الجلب التلقائي عند فتح الإضافة
- يتم جلب الأسماء تلقائياً بعد ثانية واحدة من فتح الإضافة
- يعمل بنفس طريقة الزر اليدوي
- لا يظهر رسائل خطأ إذا فشل (لأنه تلقائي)

### 2. خيار التحكم في الجلب التلقائي
- مربع اختيار في الواجهة لتفعيل/تعطيل الجلب التلقائي
- الإعداد محفوظ في تخزين المتصفح
- افتراضياً مفعل

## التفاصيل التقنية:

### الدوال المضافة:

#### 1. دالة الجلب التلقائي:
```javascript
async function autoFetchNames() {
  // التحقق من إعداد الجلب التلقائي
  const settings = await chrome.storage.sync.get('autoFetchNames');
  const autoFetchEnabled = settings.autoFetchNames !== false;

  if (!autoFetchEnabled) {
    return; // الجلب التلقائي معطل
  }

  // نفس منطق الجلب اليدوي
  // ...
}
```

#### 2. دالة تحميل الإعداد:
```javascript
async function loadAutoFetchSetting() {
  const result = await chrome.storage.sync.get('autoFetchNames');
  const autoFetchEnabled = result.autoFetchNames !== false;
  
  // تحديث مربع الاختيار
  const checkbox = document.getElementById('autoFetchToggle');
  checkbox.checked = autoFetchEnabled;
  
  // إضافة معالج الحدث
  checkbox.addEventListener('change', async (e) => {
    await chrome.storage.sync.set({ autoFetchNames: e.target.checked });
  });
}
```

### العناصر المضافة في HTML:

```html
<div class="auto-fetch-controls" style="margin-top: 10px; text-align: center;">
  <label style="font-size: 12px; color: #6c757d;">
    <input type="checkbox" id="autoFetchToggle" checked> 
    جلب الأسماء تلقائياً عند فتح الإضافة
  </label>
</div>
```

## كيف يعمل:

### 1. عند فتح الإضافة:
1. يتم تحميل الإعدادات
2. يتم تحميل حالة مربع الاختيار
3. بعد ثانية واحدة، يتم استدعاء `autoFetchNames()`
4. إذا كان الجلب التلقائي مفعل، يتم جلب الأسماء

### 2. عند تغيير الإعداد:
1. المستخدم يضغط على مربع الاختيار
2. يتم حفظ الإعداد الجديد في التخزين
3. يظهر رسالة تأكيد للمستخدم

### 3. الجلب التلقائي:
- يستخدم نفس منطق الزر اليدوي
- يجلب النص من المخرجات المخصصة
- يستخرج الأسماء ويعرضها
- لا يظهر رسائل خطأ (فشل صامت)

## المزايا:

### 1. سهولة الاستخدام:
- لا حاجة للضغط على الزر في كل مرة
- الأسماء تظهر فوراً عند فتح الإضافة

### 2. المرونة:
- يمكن تعطيل الميزة إذا لم يرغب المستخدم بها
- الزر اليدوي ما زال متوفر

### 3. الأداء:
- انتظار ثانية واحدة لضمان تحميل الصفحة
- فشل صامت لا يزعج المستخدم

## الاختبار:

### 1. اختبار الجلب التلقائي:
1. افتح الإضافة على صفحة تحتوي على أسماء
2. انتظر ثانية واحدة
3. يجب أن تظهر الأسماء تلقائياً

### 2. اختبار التحكم في الإعداد:
1. ألغِ تفعيل مربع الاختيار
2. أغلق الإضافة وافتحها مرة أخرى
3. يجب ألا تظهر الأسماء تلقائياً
4. فعل مربع الاختيار مرة أخرى
5. أغلق الإضافة وافتحها
6. يجب أن تظهر الأسماء تلقائياً

## الملفات المعدلة:
- `popup.html`: إضافة مربع الاختيار
- `popup.js`: إضافة الدوال والمنطق
